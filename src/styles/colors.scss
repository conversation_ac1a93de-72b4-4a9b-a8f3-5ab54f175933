// =============================================================================
// COLORS VARIABLES
// =============================================================================

// Primary Colors
// -----------------------------------------------------------------------------
$primary-50: #f0f9ff;
$primary-100: #e0f2fe;
$primary-200: #bae6fd;
$primary-300: #7dd3fc;
$primary-400: #38bdf8;
$primary-500: #0ea5e9;
$primary-600: #0284c7;
$primary-700: #0369a1;
$primary-800: #075985;
$primary-900: #0c4a6e;

// Semantic Colors
// -----------------------------------------------------------------------------
$color-primary: $primary-600;
$color-primary-hover: $primary-700;
$color-primary-light: $primary-100;

// Neutral Colors
// -----------------------------------------------------------------------------
$neutral-50: #fafafa;
$neutral-100: #f5f5f5;
$neutral-200: #e5e5e5;
$neutral-300: #d4d4d4;
$neutral-400: #a3a3a3;
$neutral-500: #737373;
$neutral-600: #525252;
$neutral-700: #404040;
$neutral-800: #262626;
$neutral-900: #171717;

// Text Colors
// -----------------------------------------------------------------------------
$text-primary: $neutral-900;
$text-secondary: $neutral-600;
$text-muted: $neutral-500;
$text-inverse: #ffffff;

// Background Colors
// -----------------------------------------------------------------------------
$bg-primary: #ffffff;
$bg-secondary: $neutral-50;
$bg-muted: $neutral-100;

// Border Colors
// -----------------------------------------------------------------------------
$border-light: $neutral-200;
$border-medium: $neutral-300;
$border-dark: $neutral-400;

// Status Colors
// -----------------------------------------------------------------------------
$success-50: #f0fdf4;
$success-500: #22c55e;
$success-600: #16a34a;

$warning-50: #fffbeb;
$warning-500: #f59e0b;
$warning-600: #d97706;

$error-50: #fef2f2;
$error-500: #ef4444;
$error-600: #dc2626;

$info-50: #eff6ff;
$info-500: #3b82f6;
$info-600: #2563eb;

// CSS Custom Properties (CSS Variables)
// -----------------------------------------------------------------------------
:root {
  // Primary
  --color-primary: #{$color-primary};
  --color-primary-hover: #{$color-primary-hover};
  --color-primary-light: #{$color-primary-light};
  
  // Text
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-muted: #{$text-muted};
  --text-inverse: #{$text-inverse};
  
  // Background
  --bg-primary: #{$bg-primary};
  --bg-secondary: #{$bg-secondary};
  --bg-muted: #{$bg-muted};
  
  // Border
  --border-light: #{$border-light};
  --border-medium: #{$border-medium};
  --border-dark: #{$border-dark};
  
  // Status
  --success: #{$success-500};
  --warning: #{$warning-500};
  --error: #{$error-500};
  --info: #{$info-500};
}
